[package]
name = "graphics_engine"
version = "0.1.0"
edition = "2024"
authors = ["Graphics Engine Team"]
description = "A high-performance Metal-based graphics engine for macOS"
license = "MIT"

# Optimize for performance and reliability
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 1
debug = true

[dependencies]
# Metal API bindings
metal = "0.27"
objc = "0.2"

# Windowing and events
winit = "0.29"

# Mathematics library optimized for graphics
glam = { version = "0.24", features = ["mint", "bytemuck"] }

# Memory management and serialization
bytemuck = { version = "1.14", features = ["derive"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging for debugging and performance monitoring
log = "0.4"
env_logger = "0.10"

# Performance profiling
tracy-client = { version = "0.16", optional = true }

[features]
default = []
profiling = ["tracy-client"]

# Target macOS ARM64 specifically
[target.'cfg(target_os = "macos")'.dependencies]
cocoa = "0.25"
core-graphics = "0.23"
