//! Minimal window example that just opens a window and clears it.
//! 
//! This is the simplest possible example to verify the engine works.

use graphics_engine::prelude::*;
use glam::Vec4;

fn main() -> Result<()> {
    // Initialize logging
    env_logger::init();

    log::info!("Starting Minimal Window Demo");

    // Create engine configuration
    let config = EngineConfig::new()
        .with_title("Graphics Engine - Minimal Window")
        .with_size(800, 600)
        .with_vsync(true)
        .with_debug(true);

    // Create the engine
    let mut engine = Engine::new(config)?;
    
    // Set a nice clear color
    engine.renderer_mut().set_clear_color(Vec4::new(0.2, 0.4, 0.8, 1.0));
    
    log::info!("Engine created successfully, starting main loop...");
    
    // Run the main loop
    engine.run()?;

    Ok(())
}
