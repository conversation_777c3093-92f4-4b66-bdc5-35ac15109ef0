//! Visual graphics demo showing animated 2D and 3D geometry.
//! 
//! This example demonstrates the complete graphics pipeline with:
//! - Animated 2D triangle (rotating)
//! - Animated 2D quad (pulsing)
//! - Animated 3D cube (rotating with lighting)
//! - Real-time rendering with Metal

use graphics_engine::prelude::*;
use glam::{Vec3, Vec4};

fn main() -> Result<()> {
    // Initialize logging
    env_logger::init();

    println!("🎨 Starting Visual Graphics Demo");
    println!("================================");
    println!("This demo shows:");
    println!("- Animated 2D triangle (red, rotating)");
    println!("- Animated 2D quad (green, pulsing)");
    println!("- Animated 3D cube (orange, rotating with lighting)");
    println!("- Real-time Metal rendering");
    println!();

    // Create engine configuration optimized for visual demo
    let config = EngineConfig::new()
        .with_title("Graphics Engine - Visual Demo")
        .with_size(1280, 720)
        .with_vsync(true)
        .with_debug(false) // Disable debug for smoother performance
        .with_max_frames_in_flight(3);

    // Create and configure the engine
    let mut engine = Engine::new(config)?;
    
    // Set up the visual scene
    setup_visual_scene(&mut engine)?;
    
    println!("🚀 Starting rendering loop...");
    println!("Close the window to exit.");
    
    // Run the main rendering loop
    engine.run()?;

    Ok(())
}

fn setup_visual_scene(engine: &mut Engine) -> Result<()> {
    log::info!("Setting up visual demo scene...");
    
    // Set a nice dark blue background
    engine.renderer_mut().set_clear_color(Vec4::new(0.1, 0.15, 0.25, 1.0));
    
    // Position the camera for optimal 3D viewing
    {
        let camera = engine.renderer_mut().camera_mut();
        camera.position = Vec3::new(0.0, 1.0, 4.0);
        camera.target = Vec3::new(0.0, 0.0, 0.0);
        camera.up = Vec3::Y;
    }
    
    log::info!("Visual demo scene configured:");
    log::info!("- Background: Dark blue gradient");
    log::info!("- Camera: Positioned for 3D viewing");
    log::info!("- Geometry: Triangle, quad, and cube with animations");
    
    Ok(())
}
