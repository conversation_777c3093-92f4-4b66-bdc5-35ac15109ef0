//! Basic demo showing 2D and 3D rendering capabilities.
//! 
//! This example demonstrates:
//! - Basic engine initialization
//! - 2D primitive rendering (triangle, quad, circle)
//! - 3D primitive rendering (cube, sphere)
//! - Camera controls
//! - Basic lighting

use graphics_engine::prelude::*;
use graphics_engine::geometry::{primitives_2d, primitives_3d};
use glam::{Vec3, Vec4, Quat};

fn main() -> Result<()> {
    // Initialize logging
    env_logger::init();

    log::info!("Starting Basic Graphics Engine Demo");

    // Create engine configuration
    let config = EngineConfig::new()
        .with_title("Graphics Engine - Basic Demo")
        .with_size(1280, 720)
        .with_vsync(true)
        .with_debug(true);

    // Create and run the engine
    let mut engine = Engine::new(config)?;
    
    // Set up the demo scene
    setup_demo_scene(&mut engine)?;
    
    // Run the main loop
    engine.run()?;

    Ok(())
}

fn setup_demo_scene(engine: &mut Engine) -> Result<()> {
    log::info!("Setting up basic demo scene...");
    
    // Set a nice gradient-like background color
    engine.renderer_mut().set_clear_color(Vec4::new(0.15, 0.25, 0.35, 1.0));
    
    // Position the camera for a good view of 3D objects
    {
        let camera = engine.renderer_mut().camera_mut();
        camera.position = Vec3::new(3.0, 2.0, 5.0);
        camera.target = Vec3::new(0.0, 0.0, 0.0);
    }
    
    // Create some 2D geometry for demonstration
    let _triangle_2d = primitives_2d::triangle(0.3, Vec4::new(1.0, 0.3, 0.3, 1.0));
    let _quad_2d = primitives_2d::quad(0.4, 0.3, Vec4::new(0.3, 1.0, 0.3, 1.0));
    let _circle_2d = primitives_2d::circle(0.2, 32, Vec4::new(0.3, 0.3, 1.0, 1.0));
    
    // Create some 3D geometry for demonstration
    let _cube_3d = primitives_3d::cube(1.0, Vec4::new(0.8, 0.4, 0.2, 1.0));
    let _sphere_3d = primitives_3d::sphere(0.8, 16, 32, Vec4::new(0.2, 0.8, 0.4, 1.0));
    let _plane_3d = primitives_3d::plane(4.0, 4.0, Vec4::new(0.6, 0.6, 0.6, 1.0));
    
    log::info!("Basic demo scene setup complete");
    log::info!("Created 2D primitives: triangle, quad, circle");
    log::info!("Created 3D primitives: cube, sphere, plane");
    
    Ok(())
}
