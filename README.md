# Graphics Engine

A high-performance, native GPU graphics engine built in Rust for macOS ARM architecture, utilizing the Metal API for optimal performance and reliability.

## Features

### 🚀 Performance-Focused
- **Metal API Integration**: Direct Metal API usage for maximum performance on Apple Silicon
- **Optimized Memory Management**: RAII patterns and efficient buffer management
- **Triple Buffering**: Smooth rendering with configurable frame-in-flight limits
- **Unified Memory Architecture**: Optimized for Apple Silicon's unified memory

### 🛡️ Reliability & Safety
- **Comprehensive Error Handling**: Detailed error types with context
- **Memory Safety**: Rust's ownership system prevents common graphics programming errors
- **Type-Safe Shader Interface**: Compile-time shader validation and type checking
- **Extensive Testing**: Unit tests for all core components

### 🎨 Graphics Capabilities
- **2D Rendering**: Triangles, quads, circles, and custom polygons
- **3D Rendering**: Cubes, spheres, planes, and custom meshes
- **Shader System**: Dynamic shader compilation and caching
- **Camera System**: Perspective and orthographic projections
- **Basic Lighting**: Phong lighting model with ambient, diffuse, and specular components

## Architecture

The engine is built with a modular architecture focusing on separation of concerns:

```
├── core/           # Engine core and device management
├── renderer/       # Rendering subsystem and pipelines
├── geometry/       # Vertex definitions and primitive generation
├── math/           # Mathematical utilities and transformations
└── error/          # Comprehensive error handling
```

### Core Components

- **Engine**: Main engine coordination and window management
- **MetalDevice**: Safe wrapper around Metal device functionality
- **Renderer**: High-level rendering interface with command batching
- **ShaderManager**: Dynamic shader compilation and caching system
- **Geometry System**: Efficient vertex/index buffer management

## Quick Start

### Prerequisites

- macOS with Apple Silicon (M1/M2/M3)
- Rust 1.70+ with 2024 edition
- Xcode Command Line Tools

### Basic Usage

```rust
use graphics_engine::prelude::*;
use glam::Vec4;

fn main() -> Result<()> {
    // Initialize logging
    env_logger::init();

    // Create engine configuration
    let config = EngineConfig::new()
        .with_title("My Graphics App")
        .with_size(1280, 720)
        .with_vsync(true);

    // Create and run the engine
    let mut engine = Engine::new(config)?;
    
    // Set clear color
    engine.renderer_mut().set_clear_color(Vec4::new(0.2, 0.3, 0.4, 1.0));
    
    // Run the main loop
    engine.run()?;

    Ok(())
}
```

### Creating Geometry

```rust
use graphics_engine::geometry::{primitives_2d, primitives_3d};
use glam::Vec4;

// 2D primitives
let triangle = primitives_2d::triangle(0.5, Vec4::new(1.0, 0.0, 0.0, 1.0));
let quad = primitives_2d::quad(1.0, 0.8, Vec4::new(0.0, 1.0, 0.0, 1.0));
let circle = primitives_2d::circle(0.3, 32, Vec4::new(0.0, 0.0, 1.0, 1.0));

// 3D primitives
let cube = primitives_3d::cube(1.0, Vec4::new(0.8, 0.4, 0.2, 1.0));
let sphere = primitives_3d::sphere(0.8, 16, 32, Vec4::new(0.2, 0.8, 0.4, 1.0));
```

## Examples

Run the included examples to see the engine in action:

```bash
# Minimal window example
cargo run --example minimal_window

# Basic demo with 2D and 3D primitives
cargo run --example basic_demo
```

## Testing

Run the comprehensive test suite:

```bash
cargo test
```

## Performance Characteristics

- **Memory Usage**: Optimized for Apple Silicon's unified memory architecture
- **Buffer Alignment**: 16-byte alignment for optimal performance
- **Shader Compilation**: Runtime compilation with caching for flexibility
- **Command Batching**: Efficient command buffer management

## Technical Details

### Vertex Formats

The engine supports optimized vertex formats for both 2D and 3D rendering:

- **Vertex2D**: Position (2D), texture coordinates, color (32 bytes)
- **Vertex3D**: Position (3D), normal, texture coordinates, color (48 bytes)

### Shader System

- Dynamic Metal shader compilation
- Embedded fallback shaders
- Type-safe uniform binding
- Automatic pipeline state caching

### Error Handling

Comprehensive error types covering:
- Metal device initialization
- Shader compilation
- Buffer creation
- Resource management
- Window creation

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please ensure all tests pass and follow the existing code style.

## Roadmap

- [ ] Texture loading and management
- [ ] Advanced lighting models
- [ ] Post-processing effects
- [ ] Animation system
- [ ] Scene graph
- [ ] Asset loading pipeline
