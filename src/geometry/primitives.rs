//! Primitive geometry generation utilities.
//! 
//! Provides functions to generate common geometric primitives
//! for both 2D and 3D rendering.

use glam::{Vec2, Vec3, Vec4};
use crate::geometry::{Vertex2D, Vertex3D, Mesh2D, Mesh3D, MeshBuilder};

/// 2D primitive generation
pub mod primitives_2d {
    use super::*;

    /// Create a triangle mesh
    pub fn triangle(size: f32, color: Vec4) -> Mesh2D {
        let vertices = vec![
            Vertex2D::new(Vec2::new(0.0, size), Vec2::new(0.5, 0.0), color),
            Vertex2D::new(Vec2::new(-size, -size), Vec2::new(0.0, 1.0), color),
            Vertex2D::new(Vec2::new(size, -size), Vec2::new(1.0, 1.0), color),
        ];
        
        Mesh2D::from_vertices(vertices)
    }

    /// Create a quad mesh
    pub fn quad(width: f32, height: f32, color: Vec4) -> Mesh2D {
        let half_w = width * 0.5;
        let half_h = height * 0.5;
        
        MeshBuilder::new()
            .add_vertex(Vertex2D::new(Vec2::new(-half_w, -half_h), Vec2::new(0.0, 1.0), color))
            .add_vertex(Vertex2D::new(Vec2::new(half_w, -half_h), Vec2::new(1.0, 1.0), color))
            .add_vertex(Vertex2D::new(Vec2::new(half_w, half_h), Vec2::new(1.0, 0.0), color))
            .add_vertex(Vertex2D::new(Vec2::new(-half_w, half_h), Vec2::new(0.0, 0.0), color))
            .add_quad(0, 1, 2, 3)
            .build()
    }

    /// Create a circle mesh
    pub fn circle(radius: f32, segments: u32, color: Vec4) -> Mesh2D {
        let mut builder = MeshBuilder::new();
        
        // Center vertex
        builder = builder.add_vertex(Vertex2D::new(Vec2::ZERO, Vec2::new(0.5, 0.5), color));
        
        // Perimeter vertices
        for i in 0..segments {
            let angle = 2.0 * std::f32::consts::PI * i as f32 / segments as f32;
            let x = radius * angle.cos();
            let y = radius * angle.sin();
            let tex_x = (x / radius + 1.0) * 0.5;
            let tex_y = (y / radius + 1.0) * 0.5;
            
            builder = builder.add_vertex(Vertex2D::new(
                Vec2::new(x, y),
                Vec2::new(tex_x, tex_y),
                color
            ));
        }
        
        // Create triangles
        for i in 0..segments {
            let next = if i == segments - 1 { 1 } else { (i + 2) as u16 };
            builder = builder.add_triangle(0, (i + 1) as u16, next);
        }
        
        builder.build()
    }

    /// Create a regular polygon mesh
    pub fn polygon(radius: f32, sides: u32, color: Vec4) -> Mesh2D {
        circle(radius, sides, color)
    }
}

/// 3D primitive generation
pub mod primitives_3d {
    use super::*;

    /// Create a cube mesh
    pub fn cube(size: f32, color: Vec4) -> Mesh3D {
        let half = size * 0.5;
        
        let vertices = vec![
            // Front face
            Vertex3D::new(Vec3::new(-half, -half, half), Vec3::Z, Vec2::new(0.0, 1.0), color),
            Vertex3D::new(Vec3::new(half, -half, half), Vec3::Z, Vec2::new(1.0, 1.0), color),
            Vertex3D::new(Vec3::new(half, half, half), Vec3::Z, Vec2::new(1.0, 0.0), color),
            Vertex3D::new(Vec3::new(-half, half, half), Vec3::Z, Vec2::new(0.0, 0.0), color),
            
            // Back face
            Vertex3D::new(Vec3::new(-half, -half, -half), Vec3::NEG_Z, Vec2::new(1.0, 1.0), color),
            Vertex3D::new(Vec3::new(-half, half, -half), Vec3::NEG_Z, Vec2::new(1.0, 0.0), color),
            Vertex3D::new(Vec3::new(half, half, -half), Vec3::NEG_Z, Vec2::new(0.0, 0.0), color),
            Vertex3D::new(Vec3::new(half, -half, -half), Vec3::NEG_Z, Vec2::new(0.0, 1.0), color),
            
            // Top face
            Vertex3D::new(Vec3::new(-half, half, -half), Vec3::Y, Vec2::new(0.0, 1.0), color),
            Vertex3D::new(Vec3::new(-half, half, half), Vec3::Y, Vec2::new(0.0, 0.0), color),
            Vertex3D::new(Vec3::new(half, half, half), Vec3::Y, Vec2::new(1.0, 0.0), color),
            Vertex3D::new(Vec3::new(half, half, -half), Vec3::Y, Vec2::new(1.0, 1.0), color),
            
            // Bottom face
            Vertex3D::new(Vec3::new(-half, -half, -half), Vec3::NEG_Y, Vec2::new(1.0, 1.0), color),
            Vertex3D::new(Vec3::new(half, -half, -half), Vec3::NEG_Y, Vec2::new(0.0, 1.0), color),
            Vertex3D::new(Vec3::new(half, -half, half), Vec3::NEG_Y, Vec2::new(0.0, 0.0), color),
            Vertex3D::new(Vec3::new(-half, -half, half), Vec3::NEG_Y, Vec2::new(1.0, 0.0), color),
            
            // Right face
            Vertex3D::new(Vec3::new(half, -half, -half), Vec3::X, Vec2::new(1.0, 1.0), color),
            Vertex3D::new(Vec3::new(half, half, -half), Vec3::X, Vec2::new(1.0, 0.0), color),
            Vertex3D::new(Vec3::new(half, half, half), Vec3::X, Vec2::new(0.0, 0.0), color),
            Vertex3D::new(Vec3::new(half, -half, half), Vec3::X, Vec2::new(0.0, 1.0), color),
            
            // Left face
            Vertex3D::new(Vec3::new(-half, -half, -half), Vec3::NEG_X, Vec2::new(0.0, 1.0), color),
            Vertex3D::new(Vec3::new(-half, -half, half), Vec3::NEG_X, Vec2::new(1.0, 1.0), color),
            Vertex3D::new(Vec3::new(-half, half, half), Vec3::NEG_X, Vec2::new(1.0, 0.0), color),
            Vertex3D::new(Vec3::new(-half, half, -half), Vec3::NEG_X, Vec2::new(0.0, 0.0), color),
        ];
        
        let indices = vec![
            0, 1, 2, 0, 2, 3,       // Front
            4, 5, 6, 4, 6, 7,       // Back
            8, 9, 10, 8, 10, 11,    // Top
            12, 13, 14, 12, 14, 15, // Bottom
            16, 17, 18, 16, 18, 19, // Right
            20, 21, 22, 20, 22, 23, // Left
        ];
        
        Mesh3D::from_vertices_indices(vertices, indices)
    }

    /// Create a sphere mesh using UV sphere generation
    pub fn sphere(radius: f32, rings: u32, sectors: u32, color: Vec4) -> Mesh3D {
        let mut builder = MeshBuilder::new();
        
        let ring_step = std::f32::consts::PI / rings as f32;
        let sector_step = 2.0 * std::f32::consts::PI / sectors as f32;
        
        // Generate vertices
        for i in 0..=rings {
            let ring_angle = std::f32::consts::PI / 2.0 - i as f32 * ring_step;
            let xy = radius * ring_angle.cos();
            let z = radius * ring_angle.sin();
            
            for j in 0..=sectors {
                let sector_angle = j as f32 * sector_step;
                let x = xy * sector_angle.cos();
                let y = xy * sector_angle.sin();
                
                let position = Vec3::new(x, y, z);
                let normal = position.normalize();
                let tex_coords = Vec2::new(
                    j as f32 / sectors as f32,
                    i as f32 / rings as f32
                );
                
                builder = builder.add_vertex(Vertex3D::new(position, normal, tex_coords, color));
            }
        }
        
        // Generate indices
        for i in 0..rings {
            let k1 = (i * (sectors + 1)) as u16;
            let k2 = (k1 as u32 + sectors + 1) as u16;

            for j in 0..sectors {
                let j = j as u16;
                if i != 0 {
                    builder = builder.add_triangle(k1 + j, k2 + j, k1 + j + 1);
                }
                if i != rings - 1 {
                    builder = builder.add_triangle(k1 + j + 1, k2 + j, k2 + j + 1);
                }
            }
        }
        
        builder.build()
    }

    /// Create a plane mesh
    pub fn plane(width: f32, height: f32, color: Vec4) -> Mesh3D {
        let half_w = width * 0.5;
        let half_h = height * 0.5;
        
        MeshBuilder::new()
            .add_vertex(Vertex3D::new(Vec3::new(-half_w, 0.0, -half_h), Vec3::Y, Vec2::new(0.0, 1.0), color))
            .add_vertex(Vertex3D::new(Vec3::new(half_w, 0.0, -half_h), Vec3::Y, Vec2::new(1.0, 1.0), color))
            .add_vertex(Vertex3D::new(Vec3::new(half_w, 0.0, half_h), Vec3::Y, Vec2::new(1.0, 0.0), color))
            .add_vertex(Vertex3D::new(Vec3::new(-half_w, 0.0, half_h), Vec3::Y, Vec2::new(0.0, 0.0), color))
            .add_quad(0, 1, 2, 3)
            .build()
    }
}

// Re-export for convenience
pub use primitives_2d::*;
pub use primitives_3d::*;
