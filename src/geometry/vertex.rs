//! Vertex definitions for 2D and 3D geometry.
//! 
//! Provides optimized vertex structures with proper memory layout
//! for efficient GPU transfer and rendering.

use bytemuck::{Pod, Zeroable};
use glam::{Vec2, Vec3, Vec4};

/// 2D vertex with position, texture coordinates, and color
#[repr(C)]
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Co<PERSON>, Pod, Zeroable)]
pub struct Vertex2D {
    /// Position in 2D space
    pub position: [f32; 2],
    /// Texture coordinates
    pub tex_coords: [f32; 2],
    /// Color (RGBA)
    pub color: [f32; 4],
}

impl Vertex2D {
    /// Create a new 2D vertex
    pub fn new(position: Vec2, tex_coords: Vec2, color: Vec4) -> Self {
        Self {
            position: position.to_array(),
            tex_coords: tex_coords.to_array(),
            color: color.to_array(),
        }
    }

    /// Create a vertex with position and white color
    pub fn with_position(position: Vec2) -> Self {
        Self::new(position, Vec2::ZERO, Vec4::ONE)
    }

    /// Create a vertex with position and color
    pub fn with_position_color(position: Vec2, color: Vec4) -> Self {
        Self::new(position, Vec2::ZERO, color)
    }

    /// Create a vertex with position and texture coordinates
    pub fn with_position_tex(position: Vec2, tex_coords: Vec2) -> Self {
        Self::new(position, tex_coords, Vec4::ONE)
    }

    /// Get the vertex descriptor for Metal
    pub fn descriptor() -> metal::VertexDescriptor {
        let descriptor = metal::VertexDescriptor::new();
        
        // Position attribute (location 0)
        let position_attr = descriptor.attributes().object_at(0).unwrap();
        position_attr.set_format(metal::MTLVertexFormat::Float2);
        position_attr.set_offset(0);
        position_attr.set_buffer_index(0);

        // Texture coordinates attribute (location 1)
        let tex_attr = descriptor.attributes().object_at(1).unwrap();
        tex_attr.set_format(metal::MTLVertexFormat::Float2);
        tex_attr.set_offset(8); // 2 floats * 4 bytes
        tex_attr.set_buffer_index(0);

        // Color attribute (location 2)
        let color_attr = descriptor.attributes().object_at(2).unwrap();
        color_attr.set_format(metal::MTLVertexFormat::Float4);
        color_attr.set_offset(16); // 4 floats * 4 bytes
        color_attr.set_buffer_index(0);

        // Layout
        let layout = descriptor.layouts().object_at(0).unwrap();
        layout.set_stride(std::mem::size_of::<Self>() as u64);
        layout.set_step_rate(1);
        layout.set_step_function(metal::MTLVertexStepFunction::PerVertex);

        descriptor.to_owned()
    }
}

/// 3D vertex with position, normal, texture coordinates, and color
#[repr(C)]
#[derive(Debug, Clone, Copy, Pod, Zeroable)]
pub struct Vertex3D {
    /// Position in 3D space
    pub position: [f32; 3],
    /// Normal vector
    pub normal: [f32; 3],
    /// Texture coordinates
    pub tex_coords: [f32; 2],
    /// Color (RGBA)
    pub color: [f32; 4],
}

impl Vertex3D {
    /// Create a new 3D vertex
    pub fn new(position: Vec3, normal: Vec3, tex_coords: Vec2, color: Vec4) -> Self {
        Self {
            position: position.to_array(),
            normal: normal.to_array(),
            tex_coords: tex_coords.to_array(),
            color: color.to_array(),
        }
    }

    /// Create a vertex with position and white color
    pub fn with_position(position: Vec3) -> Self {
        Self::new(position, Vec3::Y, Vec2::ZERO, Vec4::ONE)
    }

    /// Create a vertex with position and color
    pub fn with_position_color(position: Vec3, color: Vec4) -> Self {
        Self::new(position, Vec3::Y, Vec2::ZERO, color)
    }

    /// Create a vertex with position and normal
    pub fn with_position_normal(position: Vec3, normal: Vec3) -> Self {
        Self::new(position, normal, Vec2::ZERO, Vec4::ONE)
    }

    /// Create a vertex with position, normal, and texture coordinates
    pub fn with_position_normal_tex(position: Vec3, normal: Vec3, tex_coords: Vec2) -> Self {
        Self::new(position, normal, tex_coords, Vec4::ONE)
    }

    /// Get the vertex descriptor for Metal
    pub fn descriptor() -> metal::VertexDescriptor {
        let descriptor = metal::VertexDescriptor::new();
        
        // Position attribute (location 0)
        let position_attr = descriptor.attributes().object_at(0).unwrap();
        position_attr.set_format(metal::MTLVertexFormat::Float3);
        position_attr.set_offset(0);
        position_attr.set_buffer_index(0);

        // Normal attribute (location 1)
        let normal_attr = descriptor.attributes().object_at(1).unwrap();
        normal_attr.set_format(metal::MTLVertexFormat::Float3);
        normal_attr.set_offset(12); // 3 floats * 4 bytes
        normal_attr.set_buffer_index(0);

        // Texture coordinates attribute (location 2)
        let tex_attr = descriptor.attributes().object_at(2).unwrap();
        tex_attr.set_format(metal::MTLVertexFormat::Float2);
        tex_attr.set_offset(24); // 6 floats * 4 bytes
        tex_attr.set_buffer_index(0);

        // Color attribute (location 3)
        let color_attr = descriptor.attributes().object_at(3).unwrap();
        color_attr.set_format(metal::MTLVertexFormat::Float4);
        color_attr.set_offset(32); // 8 floats * 4 bytes
        color_attr.set_buffer_index(0);

        // Layout
        let layout = descriptor.layouts().object_at(0).unwrap();
        layout.set_stride(std::mem::size_of::<Self>() as u64);
        layout.set_step_rate(1);
        layout.set_step_function(metal::MTLVertexStepFunction::PerVertex);

        descriptor.to_owned()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_vertex2d_size() {
        // Ensure vertex is properly packed
        assert_eq!(std::mem::size_of::<Vertex2D>(), 32); // 2 + 2 + 4 floats * 4 bytes
    }

    #[test]
    fn test_vertex3d_size() {
        // Ensure vertex is properly packed
        assert_eq!(std::mem::size_of::<Vertex3D>(), 48); // 3 + 3 + 2 + 4 floats * 4 bytes
    }

    #[test]
    fn test_vertex2d_creation() {
        let vertex = Vertex2D::with_position(Vec2::new(1.0, 2.0));
        assert_eq!(vertex.position, [1.0, 2.0]);
        assert_eq!(vertex.color, [1.0, 1.0, 1.0, 1.0]);
    }

    #[test]
    fn test_vertex3d_creation() {
        let vertex = Vertex3D::with_position(Vec3::new(1.0, 2.0, 3.0));
        assert_eq!(vertex.position, [1.0, 2.0, 3.0]);
        assert_eq!(vertex.normal, [0.0, 1.0, 0.0]);
        assert_eq!(vertex.color, [1.0, 1.0, 1.0, 1.0]);
    }
}
