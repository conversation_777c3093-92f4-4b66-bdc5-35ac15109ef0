//! Main renderer implementation.
//! 
//! Coordinates rendering operations, manages render pipelines,
//! and provides high-level rendering interface.

use std::sync::Arc;
use std::collections::HashMap;
use metal::{RenderPassDescriptor, MTLLoadAction, MTLStoreAction};
use glam::Vec4;

use crate::{
    Result,
    core::{MetalDevice, EngineConfig},
    renderer::{RenderPipeline, PipelineType, RenderCommand, shaders::ShaderManager},
    math::Camera,
};

/// Main renderer
pub struct Renderer {
    /// Metal device reference
    device: Arc<MetalDevice>,
    /// Render pipelines cache
    pipelines: HashMap<PipelineType, RenderPipeline>,
    /// Shader manager
    shader_manager: ShaderManager,
    /// Current viewport size
    viewport_width: f32,
    viewport_height: f32,
    /// Default camera for 3D rendering
    camera: Camera,
    /// Clear color
    clear_color: Vec4,
    /// Demo geometry for rendering
    demo_triangle_buffer: Option<metal::Buffer>,
    demo_quad_buffer: Option<metal::Buffer>,
    demo_cube_buffer: Option<metal::Buffer>,
    demo_cube_index_buffer: Option<metal::Buffer>,
}

impl Renderer {
    /// Create a new renderer
    pub fn new(device: Arc<MetalDevice>, config: &EngineConfig) -> Result<Self> {
        log::info!("Initializing renderer...");

        // Initialize shader manager and load default shaders
        let mut shader_manager = ShaderManager::new(&device);
        shader_manager.load_default_shaders()?;

        let mut pipelines = HashMap::new();

        // Create 2D pipeline
        let pipeline_2d = RenderPipeline::new_with_library(
            &device,
            PipelineType::Geometry2D,
            shader_manager.get_library("default").unwrap(),
            "vertex_2d",
            "fragment_2d",
        )?;
        pipelines.insert(PipelineType::Geometry2D, pipeline_2d);

        // Create 3D pipeline
        let pipeline_3d = RenderPipeline::new_with_library(
            &device,
            PipelineType::Geometry3D,
            shader_manager.get_library("default").unwrap(),
            "vertex_3d",
            "fragment_3d",
        )?;
        pipelines.insert(PipelineType::Geometry3D, pipeline_3d);

        // Create default camera
        let camera = Camera::default_perspective(config.aspect_ratio());

        log::info!("Renderer initialized successfully");

        // Create demo geometry buffers
        let demo_triangle_buffer = Self::create_demo_triangle_buffer(&device)?;
        let demo_quad_buffer = Self::create_demo_quad_buffer(&device)?;
        let (demo_cube_buffer, demo_cube_index_buffer) = Self::create_demo_cube_buffers(&device)?;

        Ok(Self {
            device,
            pipelines,
            shader_manager,
            viewport_width: config.window_width as f32,
            viewport_height: config.window_height as f32,
            camera,
            clear_color: Vec4::new(0.1, 0.1, 0.1, 1.0),
            demo_triangle_buffer: Some(demo_triangle_buffer),
            demo_quad_buffer: Some(demo_quad_buffer),
            demo_cube_buffer: Some(demo_cube_buffer),
            demo_cube_index_buffer: Some(demo_cube_index_buffer),
        })
    }

    /// Render a frame
    pub fn render_frame(&mut self, drawable: &metal::MetalDrawableRef, delta_time: f32) -> Result<()> {
        // Create command buffer
        let command_buffer = self.device.new_command_buffer()?;
        command_buffer.set_label("Main Render");

        // Create render pass descriptor
        let render_pass_descriptor = RenderPassDescriptor::new();
        let color_attachment = render_pass_descriptor.color_attachments().object_at(0).unwrap();
        color_attachment.set_texture(Some(drawable.texture()));
        color_attachment.set_load_action(MTLLoadAction::Clear);
        color_attachment.set_store_action(MTLStoreAction::Store);
        color_attachment.set_clear_color(metal::MTLClearColor::new(
            self.clear_color.x as f64,
            self.clear_color.y as f64,
            self.clear_color.z as f64,
            self.clear_color.w as f64,
        ));

        // Create render command encoder
        let encoder = command_buffer.new_render_command_encoder(&render_pass_descriptor);
        encoder.set_label("Main Render Pass");

        // Set viewport
        encoder.set_viewport(metal::MTLViewport {
            originX: 0.0,
            originY: 0.0,
            width: self.viewport_width as f64,
            height: self.viewport_height as f64,
            znear: 0.0,
            zfar: 1.0,
        });

        // Render some basic demo content
        self.render_demo_content(&encoder, delta_time)?;

        encoder.end_encoding();

        // Present the drawable
        command_buffer.present_drawable(drawable);

        // Commit the command buffer
        command_buffer.commit();

        Ok(())
    }

    /// Render demo content (animated 2D and 3D geometry)
    fn render_demo_content(&self, encoder: &metal::RenderCommandEncoderRef, delta_time: f32) -> Result<()> {
        use std::sync::atomic::{AtomicU32, Ordering};
        static FRAME_COUNT: AtomicU32 = AtomicU32::new(0);

        let count = FRAME_COUNT.fetch_add(1, Ordering::Relaxed);
        if count % 60 == 0 {
            log::info!("Rendering frame {} (delta: {:.3}ms)", count, delta_time * 1000.0);
        }

        // Calculate animation time
        let time = count as f32 * delta_time;

        // Render 2D triangle
        if let Some(pipeline) = self.pipelines.get(&PipelineType::Geometry2D) {
            if let Some(triangle_buffer) = &self.demo_triangle_buffer {
                self.render_2d_triangle(encoder, pipeline, triangle_buffer, time)?;
            }
        }

        // Render 2D quad
        if let Some(pipeline) = self.pipelines.get(&PipelineType::Geometry2D) {
            if let Some(quad_buffer) = &self.demo_quad_buffer {
                self.render_2d_quad(encoder, pipeline, quad_buffer, time)?;
            }
        }

        // Render 3D cube
        if let Some(pipeline) = self.pipelines.get(&PipelineType::Geometry3D) {
            if let (Some(cube_buffer), Some(index_buffer)) = (&self.demo_cube_buffer, &self.demo_cube_index_buffer) {
                self.render_3d_cube(encoder, pipeline, cube_buffer, index_buffer, time)?;
            }
        }

        Ok(())
    }

    /// Render frame in fallback mode (without drawable)
    pub fn render_frame_fallback(&mut self, delta_time: f32) -> Result<()> {
        // Just run the demo content logic without actual rendering
        self.render_demo_content_fallback(delta_time)?;
        Ok(())
    }

    /// Demo content for fallback mode
    fn render_demo_content_fallback(&self, delta_time: f32) -> Result<()> {
        use std::sync::atomic::{AtomicU32, Ordering};
        static FRAME_COUNT: AtomicU32 = AtomicU32::new(0);

        let count = FRAME_COUNT.fetch_add(1, Ordering::Relaxed);
        if count % 120 == 0 {
            log::info!("Engine running - frame {} (delta: {:.3}ms) - Graphics engine architecture working!",
                      count, delta_time * 1000.0);
        }
        Ok(())
    }

    /// Handle window resize
    pub fn handle_resize(&mut self, width: u32, height: u32) -> Result<()> {
        self.viewport_width = width as f32;
        self.viewport_height = height as f32;
        
        // Update camera aspect ratio
        self.camera.aspect_ratio = width as f32 / height as f32;
        
        log::info!("Renderer viewport updated to {}x{}", width, height);
        Ok(())
    }

    /// Set clear color
    pub fn set_clear_color(&mut self, color: Vec4) {
        self.clear_color = color;
    }

    /// Get the camera
    pub fn camera(&self) -> &Camera {
        &self.camera
    }

    /// Get mutable camera
    pub fn camera_mut(&mut self) -> &mut Camera {
        &mut self.camera
    }

    /// Execute a render command
    pub fn execute_command(&mut self, _command: &RenderCommand) -> Result<()> {
        // TODO: Implement command execution
        Ok(())
    }

    /// Get viewport size
    pub fn viewport_size(&self) -> (f32, f32) {
        (self.viewport_width, self.viewport_height)
    }

    /// Create demo triangle buffer
    fn create_demo_triangle_buffer(device: &MetalDevice) -> Result<metal::Buffer> {
        use crate::geometry::primitives_2d;
        use glam::Vec4;

        let triangle = primitives_2d::triangle(0.5, Vec4::new(1.0, 0.3, 0.3, 1.0));
        let buffer = device.new_buffer_with_data(&triangle.vertices, metal::MTLResourceOptions::CPUCacheModeDefaultCache)?;
        buffer.set_label("Demo Triangle Buffer");
        Ok(buffer)
    }

    /// Create demo quad buffer
    fn create_demo_quad_buffer(device: &MetalDevice) -> Result<metal::Buffer> {
        use crate::geometry::primitives_2d;
        use glam::Vec4;

        let quad = primitives_2d::quad(0.6, 0.4, Vec4::new(0.3, 1.0, 0.3, 1.0));
        let buffer = device.new_buffer_with_data(&quad.vertices, metal::MTLResourceOptions::CPUCacheModeDefaultCache)?;
        buffer.set_label("Demo Quad Buffer");
        Ok(buffer)
    }

    /// Create demo cube buffers (vertex and index)
    fn create_demo_cube_buffers(device: &MetalDevice) -> Result<(metal::Buffer, metal::Buffer)> {
        use crate::geometry::primitives_3d;
        use glam::Vec4;

        let cube = primitives_3d::cube(1.0, Vec4::new(0.8, 0.4, 0.2, 1.0));

        let vertex_buffer = device.new_buffer_with_data(&cube.vertices, metal::MTLResourceOptions::CPUCacheModeDefaultCache)?;
        vertex_buffer.set_label("Demo Cube Vertex Buffer");

        let index_buffer = if let Some(indices) = &cube.indices {
            let buffer = device.new_buffer_with_data(indices, metal::MTLResourceOptions::CPUCacheModeDefaultCache)?;
            buffer.set_label("Demo Cube Index Buffer");
            buffer
        } else {
            return Err(crate::EngineError::metal_buffer("Cube should have indices"));
        };

        Ok((vertex_buffer, index_buffer))
    }

    /// Render a 2D triangle with animation
    fn render_2d_triangle(
        &self,
        encoder: &metal::RenderCommandEncoderRef,
        pipeline: &RenderPipeline,
        buffer: &metal::Buffer,
        time: f32,
    ) -> Result<()> {
        use glam::Mat4;

        // Set the render pipeline
        encoder.set_render_pipeline_state(pipeline.pipeline_state());

        // Create animated transform matrix
        let rotation = time * 0.5; // Rotate slowly
        let translation = glam::Vec3::new(-0.5, 0.3, 0.0); // Position on left side
        let transform = Mat4::from_rotation_translation(
            glam::Quat::from_rotation_z(rotation),
            translation,
        );

        // Create uniforms buffer
        let uniforms_data = [transform.to_cols_array()].concat();
        let uniforms_buffer = self.device.new_buffer_with_data(
            &uniforms_data,
            metal::MTLResourceOptions::CPUCacheModeDefaultCache,
        )?;

        // Set vertex buffer and uniforms
        encoder.set_vertex_buffer(0, Some(buffer), 0);
        encoder.set_vertex_buffer(1, Some(&uniforms_buffer), 0);

        // Draw the triangle
        encoder.draw_primitives(metal::MTLPrimitiveType::Triangle, 0, 3);

        Ok(())
    }

    /// Render a 2D quad with animation
    fn render_2d_quad(
        &self,
        encoder: &metal::RenderCommandEncoderRef,
        pipeline: &RenderPipeline,
        buffer: &metal::Buffer,
        time: f32,
    ) -> Result<()> {
        use glam::Mat4;

        // Set the render pipeline
        encoder.set_render_pipeline_state(pipeline.pipeline_state());

        // Create animated transform matrix
        let scale = 1.0 + 0.2 * (time * 2.0).sin(); // Pulsing scale
        let translation = glam::Vec3::new(0.5, -0.3, 0.0); // Position on right side
        let transform = Mat4::from_scale_rotation_translation(
            glam::Vec3::splat(scale),
            glam::Quat::IDENTITY,
            translation,
        );

        // Create uniforms buffer
        let uniforms_data = [transform.to_cols_array()].concat();
        let uniforms_buffer = self.device.new_buffer_with_data(
            &uniforms_data,
            metal::MTLResourceOptions::CPUCacheModeDefaultCache,
        )?;

        // Set vertex buffer and uniforms
        encoder.set_vertex_buffer(0, Some(buffer), 0);
        encoder.set_vertex_buffer(1, Some(&uniforms_buffer), 0);

        // Draw the quad (2 triangles = 6 vertices)
        encoder.draw_primitives(metal::MTLPrimitiveType::Triangle, 0, 6);

        Ok(())
    }

    /// Render a 3D cube with animation
    fn render_3d_cube(
        &self,
        encoder: &metal::RenderCommandEncoderRef,
        pipeline: &RenderPipeline,
        vertex_buffer: &metal::Buffer,
        index_buffer: &metal::Buffer,
        time: f32,
    ) -> Result<()> {
        use glam::{Mat4, Vec3};

        // Set the render pipeline
        encoder.set_render_pipeline_state(pipeline.pipeline_state());

        // Create animated model matrix
        let rotation_y = time * 0.8;
        let rotation_x = time * 0.3;
        let translation = Vec3::new(0.0, 0.0, -2.0); // Move back so we can see it
        let model = Mat4::from_rotation_translation(
            glam::Quat::from_euler(glam::EulerRot::XYZ, rotation_x, rotation_y, 0.0),
            translation,
        );

        // Get view-projection matrix from camera
        let view_projection = self.camera.view_projection_matrix();

        // Create 3D uniforms
        #[repr(C)]
        #[derive(Copy, Clone)]
        struct Uniforms3D {
            model: [[f32; 4]; 4],
            view_projection: [[f32; 4]; 4],
            light_position: [f32; 3],
            light_color: [f32; 3],
            camera_position: [f32; 3],
        }

        let uniforms = Uniforms3D {
            model: model.to_cols_array_2d(),
            view_projection: view_projection.to_cols_array_2d(),
            light_position: [2.0, 2.0, 2.0],
            light_color: [1.0, 1.0, 1.0],
            camera_position: self.camera.position.to_array(),
        };

        let uniforms_buffer = self.device.new_buffer_with_data(
            &[uniforms],
            metal::MTLResourceOptions::CPUCacheModeDefaultCache,
        )?;

        // Set vertex buffer and uniforms
        encoder.set_vertex_buffer(0, Some(vertex_buffer), 0);
        encoder.set_vertex_buffer(1, Some(&uniforms_buffer), 0);

        // Draw the cube using indices
        encoder.draw_indexed_primitives(
            metal::MTLPrimitiveType::Triangle,
            36, // 12 triangles * 3 vertices
            metal::MTLIndexType::UInt16,
            index_buffer,
            0,
        );

        Ok(())
    }
}
