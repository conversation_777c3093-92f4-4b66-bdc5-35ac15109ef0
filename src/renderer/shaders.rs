//! Shader management and compilation.
//!
//! Provides utilities for loading and compiling Metal shaders with
//! proper error handling and caching for performance.

use metal::{Library, CompileOptions};
use std::collections::HashMap;
use crate::{Result, EngineError, core::MetalDevice};

/// Shader manager for loading and caching compiled shaders
pub struct ShaderManager {
    /// Compiled shader libraries cache
    libraries: HashMap<String, Library>,
    /// Metal device reference
    device: metal::Device,
}

impl ShaderManager {
    /// Create a new shader manager
    pub fn new(device: &MetalDevice) -> Self {
        Self {
            libraries: HashMap::new(),
            device: device.device().clone(),
        }
    }

    /// Compile shader source code into a library
    pub fn compile_shader_source(&mut self, name: &str, source: &str) -> Result<()> {
        if self.libraries.contains_key(name) {
            return Ok(());
        }

        log::info!("Compiling shader: {}", name);

        let options = CompileOptions::new();
        let library = self.device.new_library_with_source(source, &options)
            .map_err(|e| EngineError::shader_compilation(
                format!("Failed to compile shader '{}': {}", name, e)
            ))?;

        self.libraries.insert(name.to_string(), library);
        Ok(())
    }

    /// Load shader from file
    pub fn load_shader_file(&mut self, name: &str, path: &str) -> Result<()> {
        if self.libraries.contains_key(name) {
            return Ok(());
        }

        log::info!("Loading shader from file: {}", path);

        let source = std::fs::read_to_string(path)
            .map_err(|e| EngineError::shader_compilation(
                format!("Failed to read shader file '{}': {}", path, e)
            ))?;

        self.compile_shader_source(name, &source)
    }

    /// Get a cached library
    pub fn get_library(&self, name: &str) -> Option<&Library> {
        self.libraries.get(name)
    }

    /// Load default shaders
    pub fn load_default_shaders(&mut self) -> Result<()> {
        // Try to load from file first, fall back to embedded shaders
        if std::path::Path::new("shaders/basic.metal").exists() {
            self.load_shader_file("default", "shaders/basic.metal")?;
        } else {
            self.compile_shader_source("default", EMBEDDED_SHADER_SOURCE)?;
        }

        log::info!("Default shaders loaded successfully");
        Ok(())
    }
}

/// Embedded shader source as fallback
const EMBEDDED_SHADER_SOURCE: &str = include_str!("../../shaders/basic.metal");
