//! Render pipeline management and state.
//! 
//! Provides abstractions for Metal render pipeline states with
//! optimized creation and caching for different geometry types.

use metal::{RenderPipelineState, RenderPipelineDescriptor, MTLPixelFormat};

use crate::{Result, EngineError, core::MetalDevice};

/// Types of render pipelines
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum PipelineType {
    /// 2D geometry pipeline
    Geometry2D,
    /// 3D geometry pipeline
    Geometry3D,
}

/// Render pipeline wrapper
pub struct RenderPipeline {
    /// Pipeline type
    pipeline_type: PipelineType,
    /// Metal render pipeline state
    pipeline_state: RenderPipelineState,
}

impl RenderPipeline {
    /// Create a new render pipeline with custom library
    pub fn new_with_library(
        device: &MetalDevice,
        pipeline_type: PipelineType,
        library: &metal::Library,
        vertex_shader: &str,
        fragment_shader: &str,
    ) -> Result<Self> {
        // Get shader functions
        let vertex_function = library.get_function(vertex_shader, None)
            .map_err(|e| EngineError::shader_compilation(
                format!("Failed to load vertex shader '{}': {}", vertex_shader, e)
            ))?;

        let fragment_function = library.get_function(fragment_shader, None)
            .map_err(|e| EngineError::shader_compilation(
                format!("Failed to load fragment shader '{}': {}", fragment_shader, e)
            ))?;

        // Create pipeline descriptor
        let descriptor = RenderPipelineDescriptor::new();
        descriptor.set_vertex_function(Some(&vertex_function));
        descriptor.set_fragment_function(Some(&fragment_function));
        
        // Set vertex descriptor based on pipeline type
        match pipeline_type {
            PipelineType::Geometry2D => {
                descriptor.set_vertex_descriptor(Some(&crate::geometry::Vertex2D::descriptor()));
            }
            PipelineType::Geometry3D => {
                descriptor.set_vertex_descriptor(Some(&crate::geometry::Vertex3D::descriptor()));
            }
        }

        // Configure color attachment
        let color_attachment = descriptor.color_attachments().object_at(0).unwrap();
        color_attachment.set_pixel_format(MTLPixelFormat::BGRA8Unorm);
        color_attachment.set_blending_enabled(true);
        color_attachment.set_rgb_blend_operation(metal::MTLBlendOperation::Add);
        color_attachment.set_alpha_blend_operation(metal::MTLBlendOperation::Add);
        color_attachment.set_source_rgb_blend_factor(metal::MTLBlendFactor::SourceAlpha);
        color_attachment.set_source_alpha_blend_factor(metal::MTLBlendFactor::SourceAlpha);
        color_attachment.set_destination_rgb_blend_factor(metal::MTLBlendFactor::OneMinusSourceAlpha);
        color_attachment.set_destination_alpha_blend_factor(metal::MTLBlendFactor::OneMinusSourceAlpha);

        // Create pipeline state
        let pipeline_state = device.device().new_render_pipeline_state(&descriptor)
            .map_err(|e| EngineError::metal_render_pipeline(
                format!("Failed to create render pipeline: {}", e)
            ))?;

        Ok(Self {
            pipeline_type,
            pipeline_state,
        })
    }

    /// Create a new render pipeline with default library
    pub fn new(
        device: &MetalDevice,
        pipeline_type: PipelineType,
        vertex_shader: &str,
        fragment_shader: &str,
    ) -> Result<Self> {
        Self::new_with_library(device, pipeline_type, device.default_library(), vertex_shader, fragment_shader)
    }

    /// Get the pipeline type
    pub fn pipeline_type(&self) -> PipelineType {
        self.pipeline_type
    }

    /// Get the Metal pipeline state
    pub fn pipeline_state(&self) -> &RenderPipelineState {
        &self.pipeline_state
    }
}
