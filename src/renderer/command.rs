//! Render command abstraction for batching and optimization.
//! 
//! Provides a command-based rendering interface that allows for
//! efficient batching and sorting of render operations.

use glam::{Vec4, Mat4};
use crate::geometry::{Vertex2D, Vertex3D};

/// High-level render commands
#[derive(Debug, <PERSON>lone)]
pub enum RenderCommand {
    /// Clear the screen with a color
    Clear { color: Vec4 },
    
    /// Draw 2D geometry
    Draw2D {
        vertices: Vec<Vertex2D>,
        indices: Option<Vec<u16>>,
        transform: Mat4,
    },
    
    /// Draw 3D geometry
    Draw3D {
        vertices: Vec<Vertex3D>,
        indices: Option<Vec<u16>>,
        transform: Mat4,
        view_projection: Mat4,
    },
    
    /// Set viewport
    SetViewport {
        x: f32,
        y: f32,
        width: f32,
        height: f32,
    },
}

impl RenderCommand {
    /// Create a clear command
    pub fn clear(color: Vec4) -> Self {
        Self::Clear { color }
    }

    /// Create a 2D draw command
    pub fn draw_2d(vertices: Vec<Vertex2D>, indices: Option<Vec<u16>>, transform: Mat4) -> Self {
        Self::Draw2D { vertices, indices, transform }
    }

    /// Create a 3D draw command
    pub fn draw_3d(
        vertices: Vec<Vertex3D>, 
        indices: Option<Vec<u16>>, 
        transform: Mat4,
        view_projection: Mat4
    ) -> Self {
        Self::Draw3D { vertices, indices, transform, view_projection }
    }

    /// Create a viewport command
    pub fn set_viewport(x: f32, y: f32, width: f32, height: f32) -> Self {
        Self::SetViewport { x, y, width, height }
    }
}
