//! Error handling for the graphics engine.
//! 
//! Provides comprehensive error types with detailed context for debugging
//! and proper error propagation throughout the engine.

use thiserror::Error;

/// Result type alias for the graphics engine
pub type Result<T> = std::result::Result<T, EngineError>;

/// Comprehensive error types for the graphics engine
#[derive(Error, Debug)]
pub enum EngineError {
    /// Metal device initialization failed
    #[error("Failed to initialize Metal device: {message}")]
    MetalDeviceInit { message: String },

    /// Metal command buffer creation failed
    #[error("Failed to create Metal command buffer: {message}")]
    MetalCommandBuffer { message: String },

    /// Metal render pipeline creation failed
    #[error("Failed to create Metal render pipeline: {message}")]
    MetalRenderPipeline { message: String },

    /// Metal buffer creation failed
    #[error("Failed to create Metal buffer: {message}")]
    MetalBuffer { message: String },

    /// Metal texture creation failed
    #[error("Failed to create Metal texture: {message}")]
    MetalTexture { message: String },

    /// Shader compilation failed
    #[error("Shader compilation failed: {message}")]
    ShaderCompilation { message: String },

    /// Resource not found
    #[error("Resource not found: {resource_type} '{name}'")]
    ResourceNotFound { resource_type: String, name: String },

    /// Invalid configuration
    #[error("Invalid configuration: {message}")]
    InvalidConfig { message: String },

    /// Window creation failed
    #[error("Failed to create window: {message}")]
    WindowCreation { message: String },

    /// Memory allocation failed
    #[error("Memory allocation failed: {message}")]
    MemoryAllocation { message: String },

    /// Generic I/O error
    #[error("I/O error: {0}")]
    Io(#[from] std::io::Error),

    /// Generic error with context
    #[error("Engine error: {0}")]
    Generic(#[from] anyhow::Error),
}

impl EngineError {
    /// Create a Metal device initialization error
    pub fn metal_device_init(message: impl Into<String>) -> Self {
        Self::MetalDeviceInit { message: message.into() }
    }

    /// Create a Metal command buffer error
    pub fn metal_command_buffer(message: impl Into<String>) -> Self {
        Self::MetalCommandBuffer { message: message.into() }
    }

    /// Create a Metal render pipeline error
    pub fn metal_render_pipeline(message: impl Into<String>) -> Self {
        Self::MetalRenderPipeline { message: message.into() }
    }

    /// Create a Metal buffer error
    pub fn metal_buffer(message: impl Into<String>) -> Self {
        Self::MetalBuffer { message: message.into() }
    }

    /// Create a Metal texture error
    pub fn metal_texture(message: impl Into<String>) -> Self {
        Self::MetalTexture { message: message.into() }
    }

    /// Create a shader compilation error
    pub fn shader_compilation(message: impl Into<String>) -> Self {
        Self::ShaderCompilation { message: message.into() }
    }

    /// Create a resource not found error
    pub fn resource_not_found(resource_type: impl Into<String>, name: impl Into<String>) -> Self {
        Self::ResourceNotFound { 
            resource_type: resource_type.into(), 
            name: name.into() 
        }
    }

    /// Create an invalid configuration error
    pub fn invalid_config(message: impl Into<String>) -> Self {
        Self::InvalidConfig { message: message.into() }
    }

    /// Create a window creation error
    pub fn window_creation(message: impl Into<String>) -> Self {
        Self::WindowCreation { message: message.into() }
    }

    /// Create a memory allocation error
    pub fn memory_allocation(message: impl Into<String>) -> Self {
        Self::MemoryAllocation { message: message.into() }
    }
}
