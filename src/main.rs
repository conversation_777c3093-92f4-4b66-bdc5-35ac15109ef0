use graphics_engine::prelude::*;
use graphics_engine::geometry::primitives_2d;
use glam::Vec4;

fn main() -> Result<()> {
    // Initialize logging
    env_logger::init();

    log::info!("Starting Graphics Engine Demo");

    // Create engine configuration
    let config = EngineConfig::new()
        .with_title("Graphics Engine Demo - Metal Rust")
        .with_size(1280, 720)
        .with_vsync(true)
        .with_debug(true);

    // Create and run the engine
    let mut engine = Engine::new(config)?;

    // Set up some demo content
    setup_demo_scene(&mut engine)?;

    engine.run()?;

    Ok(())
}

fn setup_demo_scene(engine: &mut Engine) -> Result<()> {
    log::info!("Setting up demo scene...");

    // Set a nice background color
    engine.renderer_mut().set_clear_color(Vec4::new(0.2, 0.3, 0.4, 1.0));

    // Create some basic geometry for demonstration
    let _triangle = primitives_2d::triangle(0.5, Vec4::new(1.0, 0.5, 0.2, 1.0));
    let _quad = primitives_2d::quad(0.8, 0.6, Vec4::new(0.2, 0.8, 0.3, 1.0));
    let _circle = primitives_2d::circle(0.3, 32, Vec4::new(0.8, 0.2, 0.8, 1.0));

    log::info!("Demo scene setup complete");

    Ok(())
}
