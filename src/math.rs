//! Mathematical utilities and transformations for 2D and 3D graphics.
//! 
//! Provides optimized transformation matrices and utility functions
//! built on top of the glam library for maximum performance.

use glam::{Mat4, Vec2, Vec3, Quat};

/// 2D transformation matrix with position, rotation, and scale
#[derive(Debug, <PERSON>lone, Copy)]
pub struct Transform2D {
    /// Position in 2D space
    pub position: Vec2,
    /// Rotation in radians
    pub rotation: f32,
    /// Scale factor (uniform)
    pub scale: f32,
}

impl Default for Transform2D {
    fn default() -> Self {
        Self {
            position: Vec2::ZERO,
            rotation: 0.0,
            scale: 1.0,
        }
    }
}

impl Transform2D {
    /// Create a new 2D transform
    pub fn new(position: Vec2, rotation: f32, scale: f32) -> Self {
        Self { position, rotation, scale }
    }

    /// Create a transform with only position
    pub fn from_position(position: Vec2) -> Self {
        Self { position, ..Default::default() }
    }

    /// Create a transform with only rotation
    pub fn from_rotation(rotation: f32) -> Self {
        Self { rotation, ..Default::default() }
    }

    /// Create a transform with only scale
    pub fn from_scale(scale: f32) -> Self {
        Self { scale, ..Default::default() }
    }

    /// Convert to a 4x4 transformation matrix
    pub fn to_matrix(&self) -> Mat4 {
        Mat4::from_scale_rotation_translation(
            Vec3::new(self.scale, self.scale, 1.0),
            Quat::from_rotation_z(self.rotation),
            self.position.extend(0.0),
        )
    }
}

/// 3D transformation with position, rotation, and scale
#[derive(Debug, Clone, Copy)]
pub struct Transform3D {
    /// Position in 3D space
    pub position: Vec3,
    /// Rotation quaternion
    pub rotation: Quat,
    /// Scale factor (uniform)
    pub scale: f32,
}

impl Default for Transform3D {
    fn default() -> Self {
        Self {
            position: Vec3::ZERO,
            rotation: Quat::IDENTITY,
            scale: 1.0,
        }
    }
}

impl Transform3D {
    /// Create a new 3D transform
    pub fn new(position: Vec3, rotation: Quat, scale: f32) -> Self {
        Self { position, rotation, scale }
    }

    /// Create a transform with only position
    pub fn from_position(position: Vec3) -> Self {
        Self { position, ..Default::default() }
    }

    /// Create a transform with only rotation
    pub fn from_rotation(rotation: Quat) -> Self {
        Self { rotation, ..Default::default() }
    }

    /// Create a transform with only scale
    pub fn from_scale(scale: f32) -> Self {
        Self { scale, ..Default::default() }
    }

    /// Convert to a 4x4 transformation matrix
    pub fn to_matrix(&self) -> Mat4 {
        Mat4::from_scale_rotation_translation(
            Vec3::splat(self.scale),
            self.rotation,
            self.position,
        )
    }
}

/// Camera utilities for creating view and projection matrices
pub struct Camera {
    /// Eye position
    pub position: Vec3,
    /// Target position to look at
    pub target: Vec3,
    /// Up vector
    pub up: Vec3,
    /// Field of view in radians
    pub fov: f32,
    /// Aspect ratio (width / height)
    pub aspect_ratio: f32,
    /// Near clipping plane
    pub near: f32,
    /// Far clipping plane
    pub far: f32,
}

impl Camera {
    /// Create a new camera
    pub fn new(
        position: Vec3,
        target: Vec3,
        up: Vec3,
        fov: f32,
        aspect_ratio: f32,
        near: f32,
        far: f32,
    ) -> Self {
        Self {
            position,
            target,
            up,
            fov,
            aspect_ratio,
            near,
            far,
        }
    }

    /// Create a default perspective camera
    pub fn default_perspective(aspect_ratio: f32) -> Self {
        Self::new(
            Vec3::new(0.0, 0.0, 5.0),
            Vec3::ZERO,
            Vec3::Y,
            std::f32::consts::FRAC_PI_4,
            aspect_ratio,
            0.1,
            100.0,
        )
    }

    /// Get the view matrix
    pub fn view_matrix(&self) -> Mat4 {
        Mat4::look_at_rh(self.position, self.target, self.up)
    }

    /// Get the projection matrix
    pub fn projection_matrix(&self) -> Mat4 {
        Mat4::perspective_rh(self.fov, self.aspect_ratio, self.near, self.far)
    }

    /// Get the combined view-projection matrix
    pub fn view_projection_matrix(&self) -> Mat4 {
        self.projection_matrix() * self.view_matrix()
    }
}

/// Utility functions for common mathematical operations
pub mod utils {
    use super::*;

    /// Convert degrees to radians
    pub fn deg_to_rad(degrees: f32) -> f32 {
        degrees * std::f32::consts::PI / 180.0
    }

    /// Convert radians to degrees
    pub fn rad_to_deg(radians: f32) -> f32 {
        radians * 180.0 / std::f32::consts::PI
    }

    /// Create an orthographic projection matrix for 2D rendering
    pub fn ortho_2d(left: f32, right: f32, bottom: f32, top: f32) -> Mat4 {
        Mat4::orthographic_rh(left, right, bottom, top, -1.0, 1.0)
    }

    /// Lerp between two values
    pub fn lerp(a: f32, b: f32, t: f32) -> f32 {
        a + (b - a) * t
    }

    /// Clamp a value between min and max
    pub fn clamp(value: f32, min: f32, max: f32) -> f32 {
        value.max(min).min(max)
    }
}
