//! # Graphics Engine
//! 
//! A high-performance Metal-based graphics engine for macOS ARM architecture.
//! 
//! This engine focuses on:
//! - **Performance**: Optimized Metal API usage with minimal overhead
//! - **Reliability**: Comprehensive error handling and memory safety
//! - **Code Quality**: Clean architecture with proper separation of concerns

#![deny(unsafe_op_in_unsafe_fn)]
#![warn(missing_docs, clippy::all, clippy::pedantic)]
#![allow(clippy::module_name_repetitions)]

pub mod core;
pub mod renderer;
pub mod geometry;
pub mod math;
pub mod error;

pub use error::{EngineError, Result};

/// Re-export commonly used types for convenience
pub mod prelude {
    pub use crate::core::{Engine, EngineConfig};
    pub use crate::renderer::{Renderer, RenderCommand};
    pub use crate::geometry::{Vertex2D, Vertex3D, Mesh};
    pub use crate::math::{Transform2D, Transform3D};
    pub use crate::error::{EngineError, Result};
    pub use glam::{Vec2, Vec3, Vec4, Mat4, Quat};
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::prelude::*;

    #[test]
    fn test_engine_config_creation() {
        let config = EngineConfig::new()
            .with_title("Test Engine")
            .with_size(1024, 768)
            .with_vsync(false);

        assert_eq!(config.window_title, "Test Engine");
        assert_eq!(config.window_width, 1024);
        assert_eq!(config.window_height, 768);
        assert!(!config.vsync);
        assert_eq!(config.aspect_ratio(), 1024.0 / 768.0);
    }

    #[test]
    fn test_config_validation() {
        let valid_config = EngineConfig::new();
        assert!(valid_config.validate().is_ok());

        let invalid_config = EngineConfig::new().with_size(0, 0);
        assert!(invalid_config.validate().is_err());
    }

    #[test]
    fn test_vertex_creation() {
        use crate::geometry::Vertex2D;
        use glam::{Vec2, Vec4};

        let vertex = Vertex2D::with_position(Vec2::new(1.0, 2.0));
        assert_eq!(vertex.position, [1.0, 2.0]);
        assert_eq!(vertex.color, [1.0, 1.0, 1.0, 1.0]);
    }

    #[test]
    fn test_primitive_generation() {
        use crate::geometry::primitives_2d;
        use glam::Vec4;

        let triangle = primitives_2d::triangle(1.0, Vec4::ONE);
        assert_eq!(triangle.vertex_count(), 3);
        assert!(!triangle.is_indexed());

        let quad = primitives_2d::quad(2.0, 2.0, Vec4::ONE);
        assert_eq!(quad.vertex_count(), 4);
        assert!(quad.is_indexed());
        assert_eq!(quad.primitive_count(), 2);
    }

    #[test]
    fn test_math_transforms() {
        use crate::math::Transform2D;
        use glam::Vec2;

        let transform = Transform2D::from_position(Vec2::new(1.0, 2.0));
        let matrix = transform.to_matrix();

        // Check that the translation is correct
        assert_eq!(matrix.w_axis.x, 1.0);
        assert_eq!(matrix.w_axis.y, 2.0);
    }
}
