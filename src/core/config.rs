//! Engine configuration and settings.
//! 
//! Provides configuration structures for initializing the graphics engine
//! with appropriate settings for performance and quality.

/// Configuration for the graphics engine
#[derive(Debug, Clone)]
pub struct EngineConfig {
    /// Window title
    pub window_title: String,
    /// Window width in pixels
    pub window_width: u32,
    /// Window height in pixels
    pub window_height: u32,
    /// Enable vertical sync
    pub vsync: bool,
    /// Maximum frames in flight (for triple buffering)
    pub max_frames_in_flight: usize,
    /// Enable debug mode with additional validation
    pub debug_mode: bool,
    /// Enable performance profiling
    pub enable_profiling: bool,
}

impl Default for EngineConfig {
    fn default() -> Self {
        Self {
            window_title: "Graphics Engine".to_string(),
            window_width: 1280,
            window_height: 720,
            vsync: true,
            max_frames_in_flight: 3,
            debug_mode: cfg!(debug_assertions),
            enable_profiling: false,
        }
    }
}

impl EngineConfig {
    /// Create a new engine configuration
    pub fn new() -> Self {
        Self::default()
    }

    /// Set the window title
    pub fn with_title(mut self, title: impl Into<String>) -> Self {
        self.window_title = title.into();
        self
    }

    /// Set the window size
    pub fn with_size(mut self, width: u32, height: u32) -> Self {
        self.window_width = width;
        self.window_height = height;
        self
    }

    /// Enable or disable vertical sync
    pub fn with_vsync(mut self, vsync: bool) -> Self {
        self.vsync = vsync;
        self
    }

    /// Set maximum frames in flight
    pub fn with_max_frames_in_flight(mut self, frames: usize) -> Self {
        self.max_frames_in_flight = frames.max(1).min(4); // Clamp between 1-4
        self
    }

    /// Enable debug mode
    pub fn with_debug(mut self, debug: bool) -> Self {
        self.debug_mode = debug;
        self
    }

    /// Enable profiling
    pub fn with_profiling(mut self, profiling: bool) -> Self {
        self.enable_profiling = profiling;
        self
    }

    /// Get the aspect ratio
    pub fn aspect_ratio(&self) -> f32 {
        self.window_width as f32 / self.window_height as f32
    }

    /// Validate the configuration
    pub fn validate(&self) -> crate::Result<()> {
        if self.window_width == 0 || self.window_height == 0 {
            return Err(crate::EngineError::invalid_config(
                "Window dimensions must be greater than zero"
            ));
        }

        if self.window_title.is_empty() {
            return Err(crate::EngineError::invalid_config(
                "Window title cannot be empty"
            ));
        }

        Ok(())
    }
}
