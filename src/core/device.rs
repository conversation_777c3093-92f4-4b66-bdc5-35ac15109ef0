//! Metal device abstraction and management.
//! 
//! Provides a safe wrapper around Metal device functionality with
//! proper resource management and error handling.

use metal::{Device, CommandQueue, Library};
use crate::{Result, EngineError};

/// Metal device wrapper with resource management
pub struct MetalDevice {
    /// The underlying Metal device
    device: Device,
    /// Command queue for submitting commands
    command_queue: CommandQueue,
    /// Default shader library
    default_library: Library,
}

impl MetalDevice {
    /// Create a new Metal device
    pub fn new() -> Result<Self> {
        log::info!("Initializing Metal device...");

        // Get the default Metal device
        let device = Device::system_default()
            .ok_or_else(|| EngineError::metal_device_init("No Metal device available"))?;

        log::info!("Metal device: {}", device.name());

        // Create command queue
        let command_queue = device.new_command_queue();

        // Load default shader library
        let default_library = device.new_default_library();

        log::info!("Metal device initialized successfully");

        Ok(Self {
            device,
            command_queue,
            default_library,
        })
    }

    /// Get the underlying Metal device
    pub fn device(&self) -> &Device {
        &self.device
    }

    /// Get the command queue
    pub fn command_queue(&self) -> &CommandQueue {
        &self.command_queue
    }

    /// Get the default shader library
    pub fn default_library(&self) -> &Library {
        &self.default_library
    }

    /// Create a new command buffer
    pub fn new_command_buffer(&self) -> Result<metal::CommandBuffer> {
        Ok(self.command_queue.new_command_buffer().to_owned())
    }

    /// Create a buffer with the specified length and options
    pub fn new_buffer(&self, length: u64, options: metal::MTLResourceOptions) -> Result<metal::Buffer> {
        Ok(self.device.new_buffer(length, options))
    }

    /// Create a buffer with data
    pub fn new_buffer_with_data<T>(&self, data: &[T], options: metal::MTLResourceOptions) -> Result<metal::Buffer>
    where
        T: Copy,
    {
        let byte_size = std::mem::size_of_val(data) as u64;
        let buffer = self.new_buffer(byte_size, options)?;
        
        unsafe {
            let contents = buffer.contents() as *mut T;
            std::ptr::copy_nonoverlapping(data.as_ptr(), contents, data.len());
        }
        
        Ok(buffer)
    }

    /// Get device capabilities and limits
    pub fn capabilities(&self) -> DeviceCapabilities {
        DeviceCapabilities {
            max_buffer_length: self.device.max_buffer_length(),
            supports_family_apple7: self.device.supports_family(metal::MTLGPUFamily::Apple7),
            supports_family_apple8: self.device.supports_family(metal::MTLGPUFamily::Apple8),
            supports_family_apple9: self.device.supports_family(metal::MTLGPUFamily::Apple9),
            name: self.device.name().to_string(),
        }
    }
}

/// Device capabilities and limits
#[derive(Debug, Clone)]
pub struct DeviceCapabilities {
    /// Maximum buffer length in bytes
    pub max_buffer_length: u64,
    /// Supports Apple GPU Family 7 (M1)
    pub supports_family_apple7: bool,
    /// Supports Apple GPU Family 8 (M2)
    pub supports_family_apple8: bool,
    /// Supports Apple GPU Family 9 (M3)
    pub supports_family_apple9: bool,
    /// Device name
    pub name: String,
}

impl DeviceCapabilities {
    /// Check if the device supports unified memory
    pub fn supports_unified_memory(&self) -> bool {
        // Apple Silicon devices support unified memory
        self.supports_family_apple7 || self.supports_family_apple8 || self.supports_family_apple9
    }

    /// Get the recommended buffer alignment
    pub fn buffer_alignment(&self) -> usize {
        // Apple Silicon devices prefer 16-byte alignment
        if self.supports_unified_memory() {
            16
        } else {
            256 // Conservative default for other devices
        }
    }

    /// Log device information
    pub fn log_info(&self) {
        log::info!("Device: {}", self.name);
        log::info!("Max buffer length: {} MB", self.max_buffer_length / (1024 * 1024));
        log::info!("Apple GPU Family 7 (M1): {}", self.supports_family_apple7);
        log::info!("Apple GPU Family 8 (M2): {}", self.supports_family_apple8);
        log::info!("Apple GPU Family 9 (M3): {}", self.supports_family_apple9);
        log::info!("Unified memory: {}", self.supports_unified_memory());
        log::info!("Buffer alignment: {} bytes", self.buffer_alignment());
    }
}
