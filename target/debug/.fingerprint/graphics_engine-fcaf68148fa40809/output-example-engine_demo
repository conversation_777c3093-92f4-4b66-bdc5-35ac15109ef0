{"$message_type":"diagnostic","message":"unnecessary parentheses around function argument","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"examples/engine_demo.rs","byte_start":4690,"byte_end":4691,"line_start":104,"line_end":104,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"            (i as f32 / 100.0),","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"examples/engine_demo.rs","byte_start":4707,"byte_end":4708,"line_start":104,"line_end":104,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"            (i as f32 / 100.0),","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_parens)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"examples/engine_demo.rs","byte_start":4690,"byte_end":4691,"line_start":104,"line_end":104,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"            (i as f32 / 100.0),","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"examples/engine_demo.rs","byte_start":4707,"byte_end":4708,"line_start":104,"line_end":104,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"            (i as f32 / 100.0),","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unnecessary parentheses around function argument\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mexamples/engine_demo.rs:104:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            (i as f32 / 100.0),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_parens)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0mi as f32 / 100.0\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            i as f32 / 100.0,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary parentheses around function argument","code":{"code":"unused_parens","explanation":null},"level":"warning","spans":[{"file_name":"examples/engine_demo.rs","byte_start":4722,"byte_end":4723,"line_start":105,"line_end":105,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"            ((100 - i) as f32 / 100.0),","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"examples/engine_demo.rs","byte_start":4747,"byte_end":4748,"line_start":105,"line_end":105,"column_start":38,"column_end":39,"is_primary":true,"text":[{"text":"            ((100 - i) as f32 / 100.0),","highlight_start":38,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these parentheses","code":null,"level":"help","spans":[{"file_name":"examples/engine_demo.rs","byte_start":4722,"byte_end":4723,"line_start":105,"line_end":105,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"            ((100 - i) as f32 / 100.0),","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"examples/engine_demo.rs","byte_start":4747,"byte_end":4748,"line_start":105,"line_end":105,"column_start":38,"column_end":39,"is_primary":true,"text":[{"text":"            ((100 - i) as f32 / 100.0),","highlight_start":38,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unnecessary parentheses around function argument\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mexamples/engine_demo.rs:105:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ((100 - i) as f32 / 100.0),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these parentheses\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\u001b[0m(100 - i) as f32 / 100.0\u001b[0m\u001b[0m\u001b[38;5;9m)\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            (100 - i) as f32 / 100.0,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"2 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 2 warnings emitted\u001b[0m\n\n"}
