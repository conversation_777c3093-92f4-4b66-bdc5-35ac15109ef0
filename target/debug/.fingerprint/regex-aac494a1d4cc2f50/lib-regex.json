{"rustc": 15497389221046826682, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 11738465886959544573, "path": 8527797697986448824, "deps": [[555019317135488525, "regex_automata", false, 7720818291083262929], [2779309023524819297, "aho_corasick", false, 11908778458108102120], [9408802513701742484, "regex_syntax", false, 10487599524534890642], [15932120279885307830, "memchr", false, 3943131108646775992]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-aac494a1d4cc2f50/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}