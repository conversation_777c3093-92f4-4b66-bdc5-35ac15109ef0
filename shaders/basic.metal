#include <metal_stdlib>
using namespace metal;

// Vertex input structures
struct Vertex2DIn {
    float2 position [[attribute(0)]];
    float2 tex_coords [[attribute(1)]];
    float4 color [[attribute(2)]];
};

struct Vertex3DIn {
    float3 position [[attribute(0)]];
    float3 normal [[attribute(1)]];
    float2 tex_coords [[attribute(2)]];
    float4 color [[attribute(3)]];
};

// Vertex output structures
struct Vertex2DOut {
    float4 position [[position]];
    float2 tex_coords;
    float4 color;
};

struct Vertex3DOut {
    float4 position [[position]];
    float3 world_position;
    float3 normal;
    float2 tex_coords;
    float4 color;
};

// Uniform structures
struct Uniforms2D {
    float4x4 transform;
};

struct Uniforms3D {
    float4x4 model;
    float4x4 view_projection;
    float3 light_position;
    float3 light_color;
    float3 camera_position;
};

// 2D Vertex Shader
vertex Vertex2DOut vertex_2d(Vertex2DIn in [[stage_in]],
                             constant Uniforms2D& uniforms [[buffer(1)]]) {
    Vertex2DOut out;
    out.position = uniforms.transform * float4(in.position, 0.0, 1.0);
    out.tex_coords = in.tex_coords;
    out.color = in.color;
    return out;
}

// 2D Fragment Shader
fragment float4 fragment_2d(Vertex2DOut in [[stage_in]]) {
    return in.color;
}

// 3D Vertex Shader
vertex Vertex3DOut vertex_3d(Vertex3DIn in [[stage_in]],
                             constant Uniforms3D& uniforms [[buffer(1)]]) {
    Vertex3DOut out;
    
    float4 world_pos = uniforms.model * float4(in.position, 1.0);
    out.position = uniforms.view_projection * world_pos;
    out.world_position = world_pos.xyz;
    
    // Transform normal to world space
    float3x3 normal_matrix = float3x3(uniforms.model[0].xyz, 
                                      uniforms.model[1].xyz, 
                                      uniforms.model[2].xyz);
    out.normal = normalize(normal_matrix * in.normal);
    
    out.tex_coords = in.tex_coords;
    out.color = in.color;
    
    return out;
}

// 3D Fragment Shader with Phong lighting
fragment float4 fragment_3d(Vertex3DOut in [[stage_in]],
                            constant Uniforms3D& uniforms [[buffer(1)]]) {
    float3 normal = normalize(in.normal);
    float3 light_dir = normalize(uniforms.light_position - in.world_position);
    float3 view_dir = normalize(uniforms.camera_position - in.world_position);
    float3 reflect_dir = reflect(-light_dir, normal);
    
    // Ambient lighting
    float3 ambient = 0.15 * uniforms.light_color;
    
    // Diffuse lighting
    float diff = max(dot(normal, light_dir), 0.0);
    float3 diffuse = diff * uniforms.light_color;
    
    // Specular lighting
    float spec = pow(max(dot(view_dir, reflect_dir), 0.0), 32.0);
    float3 specular = spec * uniforms.light_color * 0.5;
    
    // Combine lighting with material color
    float3 result = (ambient + diffuse + specular) * in.color.rgb;
    
    return float4(result, in.color.a);
}
